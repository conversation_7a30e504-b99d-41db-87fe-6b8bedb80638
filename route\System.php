<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/system', function () {
	
	
	$nameSpace = 'app\system\controller';
	
	// 系统-权限管理-管理员相关路由
	Route::post('admin/reset_password/:id', $nameSpace . '\permission\AdminController@resetPassword');
	Route::post('admin/avatar', $nameSpace . '\permission\AdminController@avatar');
	
	Route::get('admin/index', $nameSpace . '\permission\AdminController@index');
	Route::get('admin/detail/:id', $nameSpace . '\permission\AdminController@detail');
	Route::post('admin/add', $nameSpace . '\permission\AdminController@add');
	Route::post('admin/edit/:id', $nameSpace . '\permission\AdminController@edit');
	
	Route::post('admin/delete', $nameSpace . '\permission\AdminController@delete');
	
	// 系统-权限管理-菜单相关路由
	Route::get('menu/index', $nameSpace . '\permission\MenuController@index');
	Route::get('menu/detail/:id', $nameSpace . '\permission\MenuController@detail');
	Route::post('menu/add', $nameSpace . '\permission\MenuController@add');
	Route::post('menu/edit/:id', $nameSpace . '\permission\MenuController@edit');
	Route::post('menu/delete', $nameSpace . '\permission\MenuController@delete');
	
	// 系统-权限管理-角色相关路由
	Route::get('role/index', $nameSpace . '\permission\RoleController@index');
	Route::get('role/detail/:id', $nameSpace . '\permission\RoleController@detail');
	Route::post('role/add', $nameSpace . '\permission\RoleController@add');
	Route::post('role/edit/:id', $nameSpace . '\permission\RoleController@edit');
	Route::post('role/delete', $nameSpace . '\permission\RoleController@delete');
	
	// 系统-权限管理-部门相关路由
	Route::get('department/index', $nameSpace . '\permission\DepartmentController@index');
	Route::get('department/detail/:id', $nameSpace . '\permission\DepartmentController@detail');
	Route::post('department/add', $nameSpace . '\permission\DepartmentController@add');
	Route::post('department/edit/:id', $nameSpace . '\permission\DepartmentController@edit');
	Route::post('department/delete', $nameSpace . '\permission\DepartmentController@delete');
	
	
	// 系统-权限管理-岗位相关路由
	Route::get('post/index', $nameSpace . '\permission\PostController@index');
	Route::get('post/detail/:id', $nameSpace . '\permission\PostController@detail');
	Route::post('post/add', $nameSpace . '\permission\PostController@add');
	Route::post('post/edit/:id', $nameSpace . '\permission\PostController@edit');
	Route::post('post/delete', $nameSpace . '\permission\PostController@delete');
	
	// 系统配置相关路由
	Route::get('config/detail', $nameSpace . '\ConfigController@detail');
	Route::post('config/save', $nameSpace . '\ConfigController@save');
	
	// 租户配置相关路由
	Route::get('tenant/config/detail', $nameSpace . '\tenant\TenantConfigController@detail');
	Route::post('tenant/config/save', $nameSpace . '\tenant\TenantConfigController@save');
	
	// 登录日志
	Route::get('log/login/index', $nameSpace . '\log\LoginController@index');
	Route::get('log/login/detail/:id', $nameSpace . '\log\LoginController@detail');
	Route::post('log/login/delete', $nameSpace . '\log\LoginController@delete');
	
	// 操作日志
	Route::get('log/operation/index', $nameSpace . '\log\OperationController@index');
	Route::get('log/operation/detail/:id', $nameSpace . '\log\OperationController@detail');
	Route::post('log/operation/delete', $nameSpace . '\log\OperationController@delete');
	
	Route::get('attachment/index', $nameSpace . '\AttachmentController@index');
	Route::get('attachment/userFiles', $nameSpace . '\AttachmentController@userFiles');
	Route::post('attachment/delete', $nameSpace . '\AttachmentController@delete');
	Route::post('attachment/batchMove', $nameSpace . '\AttachmentController@batchMove');
	
	// 附件统计信息
//	Route::get('attachment/stats', $nameSpace . '\AttachmentController@stats');
	
	Route::get('attachment_cat/index', $nameSpace . '\AttachmentCatController@index');
	Route::post('attachment_cat/add', $nameSpace . '\AttachmentCatController@add');
	Route::post('attachment_cat/edit/:id', $nameSpace . '\AttachmentCatController@edit');
	Route::post('attachment_cat/delete', $nameSpace . '\AttachmentCatController@delete');
	
	// 租户相关路由
	Route::get('tenant/index', $nameSpace . '\tenant\TenantController@index');
	Route::get('tenant/detail/:id', $nameSpace . '\tenant\TenantController@detail');
	Route::post('tenant/add', $nameSpace . '\tenant\TenantController@add');
	Route::post('tenant/edit/:id', $nameSpace . '\tenant\TenantController@edit');
	Route::post('tenant/delete', $nameSpace . '\tenant\TenantController@delete');
	
	// 文章分类相关路由
	Route::get('article_category/index', $nameSpace . '\ArticleCategoryController@index');
	Route::get('article_category/detail/:id', $nameSpace . '\ArticleCategoryController@detail');
	Route::post('article_category/add', $nameSpace . '\ArticleCategoryController@add');
	Route::post('article_category/edit/:id', $nameSpace . '\ArticleCategoryController@edit');
	Route::post('article_category/delete', $nameSpace . '\ArticleCategoryController@delete');
	Route::post('article_category/updateField', $nameSpace . '\ArticleCategoryController@updateField');
	
	// 文章
	Route::get('article/index', $nameSpace . '\ArticleController@index');
	Route::get('article/detail/:id', $nameSpace . '\ArticleController@detail');
	Route::post('article/add', $nameSpace . '\ArticleController@add');
	Route::post('article/edit/:id', $nameSpace . '\ArticleController@edit');
	Route::post('article/delete', $nameSpace . '\ArticleController@delete');
	Route::post('article/status/:id', $nameSpace . '\ArticleController@status');
	Route::post('article/updateField', $nameSpace . '\ArticleController@updateField');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class,
//	     OperationLogMiddleware::class
     ]);

